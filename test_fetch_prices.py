import csv
import requests
import time
import json

def fetch_nse_price(symbol):
    """Fetch current price from NSE using Yahoo Finance API"""
    try:
        # Add .NS suffix for NSE stocks in Yahoo Finance
        yahoo_symbol = f"{symbol}.NS"
        url = f"https://query1.finance.yahoo.com/v8/finance/chart/{yahoo_symbol}"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'chart' in data and data['chart']['result']:
                result = data['chart']['result'][0]
                if 'meta' in result and 'regularMarketPrice' in result['meta']:
                    return result['meta']['regularMarketPrice']
        return None
    except Exception as e:
        print(f"Error fetching {symbol}: {str(e)}")
        return None

# Test with first 5 stocks
symbols = []
with open('EQUITY_L.csv', 'r', encoding='utf-8') as file:
    csv_reader = csv.reader(file)
    next(csv_reader)  # Skip header
    for i, row in enumerate(csv_reader):
        if i >= 5:  # Only get first 5
            break
        if row:
            symbols.append(row[0])

print("Testing with symbols:", symbols)
print("\nFetching prices...")

for symbol in symbols:
    price = fetch_nse_price(symbol)
    if price:
        status = "✓ Above ₹15" if price > 15 else "✗ Below ₹15"
        print(f"{symbol}: ₹{price:.2f} - {status}")
    else:
        print(f"{symbol}: Price not found")
    time.sleep(1)  # Rate limiting