import csv
import yfinance as yf
import time
from datetime import datetime

def fetch_bse_price(symbol):
    """Fetch current price from BSE using yfinance library"""
    try:
        # Add .BO suffix for BSE stocks in Yahoo Finance
        yahoo_symbol = f"{symbol}.BO"

        # Create ticker object
        ticker = yf.Ticker(yahoo_symbol)

        # Method 1: Try to get current price using info method
        try:
            info = ticker.info

            # Try different price fields that might be available
            price_fields = ['currentPrice', 'regularMarketPrice', 'previousClose', 'ask', 'bid']

            for field in price_fields:
                if field in info and info[field] is not None:
                    return info[field]
        except:
            pass

        # Method 2: Try to get price from history (last trading day)
        try:
            hist = ticker.history(period="1d")
            if not hist.empty:
                return hist['Close'].iloc[-1]
        except:
            pass

        # Method 3: Try fast_info (newer yfinance feature)
        try:
            fast_info = ticker.fast_info
            if hasattr(fast_info, 'last_price') and fast_info.last_price:
                return fast_info.last_price
        except:
            pass

        return None
    except Exception as e:
        print(f"Error fetching {symbol}: {str(e)}")
        return None

def main():
    # Read stock symbols from Equity.csv
    symbols = []
    with open('Equity.csv', 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        next(csv_reader)  # Skip header
        for row in csv_reader:
            if row:  # Check if row is not empty
                symbols.append(row[0])  # SYMBOL is the first column
    
    print(f"Total symbols to process: {len(symbols)}")
    
    # Fetch prices and filter stocks above 15 rs
    filtered_stocks = []
    processed = 0
    
    # Process in batches to avoid overwhelming the API
    batch_size = 50
    
    for i in range(0, len(symbols), batch_size):
        batch = symbols[i:i+batch_size]
        print(f"\nProcessing batch {i//batch_size + 1} ({i+1} to {min(i+batch_size, len(symbols))})")
        
        for symbol in batch:
            price = fetch_bse_price(symbol)
            processed += 1
            
            if price and price > 15:
                filtered_stocks.append({
                    'symbol': symbol,
                    'price': price
                })
                print(f"✓ {symbol}: ₹{price:.2f}")
            elif price:
                print(f"✗ {symbol}: ₹{price:.2f} (below 15)")
            else:
                print(f"- {symbol}: Price not found")
            
            # Rate limiting to avoid API restrictions
            time.sleep(1.0)  # Increased delay for yfinance stability
            
            # Save progress periodically
            if processed % 100 == 0:
                save_filtered_stocks(filtered_stocks)
                print(f"\nProgress saved. Processed: {processed}/{len(symbols)}")
    
    # Final save
    save_filtered_stocks(filtered_stocks)
    print(f"\n\nFiltering complete!")
    print(f"Total stocks processed: {processed}")
    print(f"Stocks above ₹15: {len(filtered_stocks)}")

def save_filtered_stocks(filtered_stocks):
    """Save filtered stocks to CSV file"""
    with open('filtered_stocks_above_15.csv', 'w', newline='', encoding='utf-8') as file:
        if filtered_stocks:
            writer = csv.DictWriter(file, fieldnames=['symbol', 'price'])
            writer.writeheader()
            writer.writerows(filtered_stocks)
    
    # Also save just the symbols
    with open('symbols_above_15.txt', 'w', encoding='utf-8') as file:
        for stock in filtered_stocks:
            file.write(stock['symbol'] + '\n')

if __name__ == "__main__":
    main()